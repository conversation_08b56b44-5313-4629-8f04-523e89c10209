"""
Configuration settings for image quality assessment methods
"""

# Quality Assessment Method Configuration
# Options: 'opencv', 'llm', 'both'
DEFAULT_ASSESSMENT_METHOD = 'llm'

# LLM Configuration
LLM_MODEL = "claude-3-7-sonnet-20250109"  # or "claude-opus-4-20250514" for higher accuracy
LLM_MAX_TOKENS = 2000

# Assessment Thresholds
QUALITY_SCORE_THRESHOLD = 70  # Minimum score for passing (0-100 scale)
CONFIDENCE_THRESHOLD = 0.7    # Minimum confidence for reliable assessment

# Output Configuration
SAVE_DETAILED_RESULTS = True
SAVE_INDIVIDUAL_FILES = True
RESULTS_DIRECTORY = "quality_reports"

# Workflow Integration Settings
ASSESSMENT_METHODS = {
    'development': 'llm',      # Use LLM for development/testing
    'production': 'both',      # Use both methods for production
    'fast': 'opencv',          # Use OpenCV for fast processing
    'accurate': 'llm'          # Use LLM for most accurate results
}

def get_assessment_method(mode: str = 'development') -> str:
    """
    Get the appropriate assessment method for the given mode
    
    Args:
        mode: Operating mode ('development', 'production', 'fast', 'accurate')
        
    Returns:
        Assessment method string
    """
    return ASSESSMENT_METHODS.get(mode, DEFAULT_ASSESSMENT_METHOD)

def get_llm_config() -> dict:
    """Get LLM configuration settings"""
    return {
        'model': LLM_MODEL,
        'max_tokens': LLM_MAX_TOKENS
    }

def get_quality_thresholds() -> dict:
    """Get quality assessment thresholds"""
    return {
        'quality_score_threshold': QUALITY_SCORE_THRESHOLD,
        'confidence_threshold': CONFIDENCE_THRESHOLD
    }
