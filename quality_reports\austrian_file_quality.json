{"image_path": "expense_files\\austrian_file.png", "document_type": "receipt", "image_type_detection": {"is_digital_screenshot": "True", "confidence": 0.83, "image_subtype": "mobile_app_screenshot", "indicators": {"low_color_variety": true, "matches_common_resolution": false, "has_regular_edges": "True", "has_uniform_background": true, "has_many_straight_lines": true, "low_histogram_entropy": "True"}, "metadata": {"unique_colors": 5914, "edge_density": 0.023, "histogram_entropy": "0.87"}}, "timestamp": "2025-07-16T00:19:56.426083", "processing_time_seconds": 6.18, "overall_assessment": {"score": 91.5, "level": "Good", "pass_fail": "True", "issues_summary": [], "recommendations": ["📱 This is a mobile_app_screenshot - physical document checks have been adjusted.", "✅ Resolution quality is excellent for document processing.", "📸 Motion blur detected (horizontal). Use a tripod or scanner for better results.", "✅ Digital image with clean background.", "✅ Digital screenshot - no physical boundaries to check."]}, "detailed_results": {"resolution": {"dimensions": {"width": 1700, "height": 2200, "megapixels": 3.74}, "dpi": {"horizontal": 544.0, "vertical": 259.0, "average": 401.0}, "quality": {"score": 100.0, "level": "Digital Quality", "meets_ocr_requirements": true}, "aspect_ratio": {"actual": 0.77, "expected": 0.37, "deviation_percent": 110.2}, "recommendations": ["✅ Resolution quality is excellent for document processing."]}, "blur": {"metrics": {"laplacian_variance": 365.34, "is_blurry": false, "blur_score": 70.07, "blur_level": "<PERSON>"}, "motion_blur": {"detected": "True", "score": 2.9217336898395723, "direction": "horizontal"}, "focus_distribution": {"sharp_areas_percent": 23.7, "uniform_sharpness": false}, "recommendations": ["📸 Motion blur detected (horizontal). Use a tripod or scanner for better results."]}, "glare": {"exposure_metrics": {"mean_brightness": 247.4, "overexposed_percent": "93.54", "is_overexposed": "True", "contrast_ratio": 0.13}, "glare_analysis": {"glare_score": 95.0, "glare_level": "None (Digital)", "num_glare_spots": 47, "glare_coverage_percent": 92.92, "glare_patterns": {"type": "flash", "description": "Camera flash reflection detected"}}, "affected_regions": [{"bbox": ["0", "0", "1700", "2200"], "center": [854, 1104], "area": "3298782", "intensity": 250.1054090909091}, {"bbox": ["1260", "377", "10", "24"], "center": [1264, 388], "area": "206", "intensity": 252.6625}, {"bbox": ["795", "526", "12", "14"], "center": [799, 533], "area": "105", "intensity": 246.79166666666666}, {"bbox": ["295", "1014", "95", "28"], "center": [348, 1026], "area": "1695", "intensity": 224.84624060150375}, {"bbox": ["395", "1014", "162", "28"], "center": [483, 1026], "area": "3444", "intensity": 234.5462962962963}], "recommendations": ["✅ Digital image with clean background."]}, "completeness": {"boundary_detected": true, "completeness_score": 100.0, "completeness_level": "Digital Document", "edge_analysis": {"edge_coverage": 100.0, "has_gaps": false, "num_gaps": 0}, "corner_analysis": {"visible_corners": 4, "missing_corners": [], "is_rectangular": true}, "issues": [], "boundary_points": [], "recommendations": ["✅ Digital screenshot - no physical boundaries to check."]}, "damage": {"damage_score": 100.0, "damage_level": "Digital (No Physical Damage)", "damage_types": [], "stain_analysis": {"count": 0, "coverage_percent": 0.0, "regions": []}, "tear_analysis": {"count": 0, "max_length": 0, "regions": []}, "fold_analysis": {"count": 0, "pattern": "none", "lines": []}, "recommendations": ["✅ Digital image - no physical damage possible."]}}, "score_breakdown": {"resolution": {"score": 100.0, "weight": 0.2, "contribution": 20.0}, "blur": {"score": 70.07, "weight": 0.25, "contribution": 17.5175}, "glare": {"score": 95.0, "weight": 0.2, "contribution": 19.0}, "completeness": {"score": 100.0, "weight": 0.2, "contribution": 20.0}, "damage": {"score": 100.0, "weight": 0.15, "contribution": 15.0}}, "assessment_method": "opencv", "quality_passed": "True", "quality_score": 91.5, "quality_level": "Good", "main_issues": [], "top_recommendations": ["📱 This is a mobile_app_screenshot - physical document checks have been adjusted.", "✅ Resolution quality is excellent for document processing.", "📸 Motion blur detected (horizontal). Use a tripod or scanner for better results."]}