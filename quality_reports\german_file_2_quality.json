{"image_path": "expense_files\\german_file_2.png", "document_type": "receipt", "image_type_detection": {"is_digital_screenshot": "True", "confidence": 0.83, "image_subtype": "mobile_app_screenshot", "indicators": {"low_color_variety": true, "matches_common_resolution": false, "has_regular_edges": "True", "has_uniform_background": true, "has_many_straight_lines": true, "low_histogram_entropy": "True"}, "metadata": {"unique_colors": 65967, "edge_density": 0.016, "histogram_entropy": "5.04"}}, "timestamp": "2025-07-16T00:20:02.228590", "processing_time_seconds": 5.79, "overall_assessment": {"score": 59.1, "level": "Poor", "pass_fail": "False", "issues_summary": ["Image blur detected (<PERSON><PERSON><PERSON>)", "Severe glare detected"], "recommendations": ["📱 This is a mobile_app_screenshot - physical document checks have been adjusted.", "⚠️ Significant glare detected. Adjust lighting and recapture.", "📈 Image is slightly blurry. Consider recapturing for optimal results.", "✅ Resolution quality is excellent for document processing.", "🔄 Image is blurry. Ensure camera is focused and stable during capture."]}, "detailed_results": {"resolution": {"dimensions": {"width": 1700, "height": 2200, "megapixels": 3.74}, "dpi": {"horizontal": 544.0, "vertical": 259.0, "average": 401.0}, "quality": {"score": 100.0, "level": "Digital Quality", "meets_ocr_requirements": true}, "aspect_ratio": {"actual": 0.77, "expected": 0.37, "deviation_percent": 110.2}, "recommendations": ["✅ Resolution quality is excellent for document processing."]}, "blur": {"metrics": {"laplacian_variance": 123.88, "is_blurry": true, "blur_score": 16.42, "blur_level": "S<PERSON><PERSON>"}, "motion_blur": {"detected": "True", "score": 2.6347187165775403, "direction": "vertical"}, "focus_distribution": {"sharp_areas_percent": 8.2, "uniform_sharpness": true}, "recommendations": ["🔄 Image is blurry. Ensure camera is focused and stable during capture.", "📸 Motion blur detected (vertical). Use a tripod or scanner for better results.", "📈 Image is slightly blurry. Consider recapturing for optimal results."]}, "glare": {"exposure_metrics": {"mean_brightness": 203.2, "overexposed_percent": "39.69", "is_overexposed": "True", "contrast_ratio": 0.32}, "glare_analysis": {"glare_score": 0, "glare_level": "Severe", "num_glare_spots": 1, "glare_coverage_percent": 39.68, "glare_patterns": {"type": "flash", "description": "Camera flash reflection detected"}}, "affected_regions": [{"bbox": ["0", "0", "1700", "2200"], "center": [848, 1149], "area": "1484066", "intensity": 204.73730534759358}], "recommendations": ["📷 Image is overexposed. Reduce camera exposure or lighting.", "🔦 Disable camera flash to avoid reflections.", "⚠️ Significant glare detected. Adjust lighting and recapture."]}, "completeness": {"boundary_detected": true, "completeness_score": 100.0, "completeness_level": "Digital Document", "edge_analysis": {"edge_coverage": 100.0, "has_gaps": false, "num_gaps": 0}, "corner_analysis": {"visible_corners": 4, "missing_corners": [], "is_rectangular": true}, "issues": [], "boundary_points": [], "recommendations": ["✅ Digital screenshot - no physical boundaries to check."]}, "damage": {"damage_score": 100.0, "damage_level": "Digital (No Physical Damage)", "damage_types": [], "stain_analysis": {"count": 0, "coverage_percent": 0.0, "regions": []}, "tear_analysis": {"count": 0, "max_length": 0, "regions": []}, "fold_analysis": {"count": 0, "pattern": "none", "lines": []}, "recommendations": ["✅ Digital image - no physical damage possible."]}}, "score_breakdown": {"resolution": {"score": 100.0, "weight": 0.2, "contribution": 20.0}, "blur": {"score": 16.42, "weight": 0.25, "contribution": 4.105}, "glare": {"score": 0, "weight": 0.2, "contribution": 0.0}, "completeness": {"score": 100.0, "weight": 0.2, "contribution": 20.0}, "damage": {"score": 100.0, "weight": 0.15, "contribution": 15.0}}, "assessment_method": "opencv", "quality_passed": "False", "quality_score": 59.1, "quality_level": "Poor", "main_issues": ["Image blur detected (<PERSON><PERSON><PERSON>)", "Severe glare detected"], "top_recommendations": ["📱 This is a mobile_app_screenshot - physical document checks have been adjusted.", "⚠️ Significant glare detected. Adjust lighting and recapture.", "📈 Image is slightly blurry. Consider recapturing for optimal results."]}