{"image_path": "expense_files\\german_file_3.png", "document_type": "receipt", "image_type_detection": {"is_digital_screenshot": "True", "confidence": 0.67, "image_subtype": "mobile_app_screenshot", "indicators": {"low_color_variety": true, "matches_common_resolution": false, "has_regular_edges": "False", "has_uniform_background": true, "has_many_straight_lines": true, "low_histogram_entropy": "True"}, "metadata": {"unique_colors": 113890, "edge_density": 0.051, "histogram_entropy": "5.33"}}, "timestamp": "2025-07-15T23:55:17.737536", "processing_time_seconds": 6.16, "overall_assessment": {"score": 66.1, "level": "Poor", "pass_fail": "False", "issues_summary": ["Severe glare detected"], "recommendations": ["📱 This is a mobile_app_screenshot - physical document checks have been adjusted.", "⚠️ Significant glare detected. Adjust lighting and recapture.", "✅ Resolution quality is excellent for document processing.", "📸 Motion blur detected (vertical). Use a tripod or scanner for better results.", "📷 Image is overexposed. Reduce camera exposure or lighting."]}, "detailed_results": {"resolution": {"dimensions": {"width": 1700, "height": 2200, "megapixels": 3.74}, "dpi": {"horizontal": 544.0, "vertical": 259.0, "average": 401.0}, "quality": {"score": 100.0, "level": "Digital Quality", "meets_ocr_requirements": true}, "aspect_ratio": {"actual": 0.77, "expected": 0.37, "deviation_percent": 110.2}, "recommendations": ["✅ Resolution quality is excellent for document processing."]}, "blur": {"metrics": {"laplacian_variance": 249.94, "is_blurry": false, "blur_score": 44.43, "blur_level": "<PERSON>"}, "motion_blur": {"detected": "True", "score": 4.221104010695187, "direction": "vertical"}, "focus_distribution": {"sharp_areas_percent": 26.2, "uniform_sharpness": false}, "recommendations": ["📸 Motion blur detected (vertical). Use a tripod or scanner for better results."]}, "glare": {"exposure_metrics": {"mean_brightness": 169.1, "overexposed_percent": "39.68", "is_overexposed": "True", "contrast_ratio": 0.52}, "glare_analysis": {"glare_score": 0, "glare_level": "Severe", "num_glare_spots": 1, "glare_coverage_percent": 39.68, "glare_patterns": {"type": "flash", "description": "Camera flash reflection detected"}}, "affected_regions": [{"bbox": ["0", "0", "1700", "2200"], "center": [848, 1149], "area": "1484066", "intensity": 180.08911764705883}], "recommendations": ["📷 Image is overexposed. Reduce camera exposure or lighting.", "🔦 Disable camera flash to avoid reflections.", "⚠️ Significant glare detected. Adjust lighting and recapture."]}, "completeness": {"boundary_detected": true, "completeness_score": 100.0, "completeness_level": "Digital Document", "edge_analysis": {"edge_coverage": 100.0, "has_gaps": false, "num_gaps": 0}, "corner_analysis": {"visible_corners": 4, "missing_corners": [], "is_rectangular": true}, "issues": [], "boundary_points": [], "recommendations": ["✅ Digital screenshot - no physical boundaries to check."]}, "damage": {"damage_score": 100.0, "damage_level": "Digital (No Physical Damage)", "damage_types": [], "stain_analysis": {"count": 0, "coverage_percent": 0.0, "regions": []}, "tear_analysis": {"count": 0, "max_length": 0, "regions": []}, "fold_analysis": {"count": 0, "pattern": "none", "lines": []}, "recommendations": ["✅ Digital image - no physical damage possible."]}}, "score_breakdown": {"resolution": {"score": 100.0, "weight": 0.2, "contribution": 20.0}, "blur": {"score": 44.43, "weight": 0.25, "contribution": 11.1075}, "glare": {"score": 0, "weight": 0.2, "contribution": 0.0}, "completeness": {"score": 100.0, "weight": 0.2, "contribution": 20.0}, "damage": {"score": 100.0, "weight": 0.15, "contribution": 15.0}}, "assessment_method": "opencv", "quality_passed": "False", "quality_score": 66.1, "quality_level": "Poor", "main_issues": ["Severe glare detected"], "top_recommendations": ["📱 This is a mobile_app_screenshot - physical document checks have been adjusted.", "⚠️ Significant glare detected. Adjust lighting and recapture.", "✅ Resolution quality is excellent for document processing."]}