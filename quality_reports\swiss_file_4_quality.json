{"image_path": "expense_files\\swiss_file_4.jpg", "document_type": "receipt", "image_type_detection": {"is_digital_screenshot": "True", "confidence": 0.5, "image_subtype": "mobile_app_screenshot", "indicators": {"low_color_variety": true, "matches_common_resolution": false, "has_regular_edges": "False", "has_uniform_background": true, "has_many_straight_lines": true, "low_histogram_entropy": "False"}, "metadata": {"unique_colors": 11662, "edge_density": 0.082, "histogram_entropy": "7.22"}}, "timestamp": "2025-07-16T00:08:08.507982", "processing_time_seconds": 0.24, "overall_assessment": {"score": 87.6, "level": "Good", "pass_fail": "True", "issues_summary": [], "recommendations": ["📱 This is a mobile_app_screenshot - physical document checks have been adjusted.", "⚠️ Resolution too low for reliable OCR. Rescan at higher resolution.", "🔄 Image quality insufficient. Please capture again with better camera/scanner settings.", "✅ Image sharpness is excellent.", "🔦 Disable camera flash to avoid reflections."]}, "detailed_results": {"resolution": {"dimensions": {"width": 338, "height": 450, "megapixels": 0.15}, "dpi": {"horizontal": 72, "vertical": 72, "average": 72.0}, "quality": {"score": 40.0, "level": "Digital Quality", "meets_ocr_requirements": false}, "aspect_ratio": {"actual": 0.75, "expected": 0.37, "deviation_percent": 104.3}, "recommendations": ["⚠️ Resolution too low for reliable OCR. Rescan at higher resolution.", "🔄 Image quality insufficient. Please capture again with better camera/scanner settings."]}, "blur": {"metrics": {"laplacian_variance": 4385.3, "is_blurry": false, "blur_score": 100.0, "blur_level": "Very Sharp"}, "motion_blur": {"detected": "False", "score": 9.246278763971072, "direction": "vertical"}, "focus_distribution": {"sharp_areas_percent": 85.4, "uniform_sharpness": true}, "recommendations": ["✅ Image sharpness is excellent."]}, "glare": {"exposure_metrics": {"mean_brightness": 139.5, "overexposed_percent": "0.73", "is_overexposed": "False", "contrast_ratio": 0.57}, "glare_analysis": {"glare_score": 98.1, "glare_level": "None", "num_glare_spots": 1, "glare_coverage_percent": 0.14, "glare_patterns": {"type": "flash", "description": "Camera flash reflection detected"}}, "affected_regions": [{"bbox": ["246", "0", "29", "23"], "center": [261, 8], "area": "219", "intensity": 242.02098950524737}], "recommendations": ["🔦 Disable camera flash to avoid reflections."]}, "completeness": {"boundary_detected": true, "completeness_score": 100.0, "completeness_level": "Digital Document", "edge_analysis": {"edge_coverage": 100.0, "has_gaps": false, "num_gaps": 0}, "corner_analysis": {"visible_corners": 4, "missing_corners": [], "is_rectangular": true}, "issues": [], "boundary_points": [], "recommendations": ["✅ Digital screenshot - no physical boundaries to check."]}, "damage": {"damage_score": 100.0, "damage_level": "Digital (No Physical Damage)", "damage_types": [], "stain_analysis": {"count": 0, "coverage_percent": 0.0, "regions": []}, "tear_analysis": {"count": 0, "max_length": 0, "regions": []}, "fold_analysis": {"count": 0, "pattern": "none", "lines": []}, "recommendations": ["✅ Digital image - no physical damage possible."]}}, "score_breakdown": {"resolution": {"score": 40.0, "weight": 0.2, "contribution": 8.0}, "blur": {"score": 100.0, "weight": 0.25, "contribution": 25.0}, "glare": {"score": 98.1, "weight": 0.2, "contribution": 19.62}, "completeness": {"score": 100.0, "weight": 0.2, "contribution": 20.0}, "damage": {"score": 100.0, "weight": 0.15, "contribution": 15.0}}, "assessment_method": "opencv", "quality_passed": "True", "quality_score": 87.6, "quality_level": "Good", "main_issues": [], "top_recommendations": ["📱 This is a mobile_app_screenshot - physical document checks have been adjusted.", "⚠️ Resolution too low for reliable OCR. Rescan at higher resolution.", "🔄 Image quality insufficient. Please capture again with better camera/scanner settings."]}