{"image_path": "expense_files\\swiss_file_5.jpg", "document_type": "receipt", "image_type_detection": {"is_digital_screenshot": "True", "confidence": 0.67, "image_subtype": "mobile_app_screenshot", "indicators": {"low_color_variety": true, "matches_common_resolution": false, "has_regular_edges": "True", "has_uniform_background": true, "has_many_straight_lines": true, "low_histogram_entropy": "False"}, "metadata": {"unique_colors": 173086, "edge_density": 0.043, "histogram_entropy": "6.79"}}, "timestamp": "2025-07-15T23:56:11.265622", "processing_time_seconds": 13.17, "overall_assessment": {"score": 73.5, "level": "Acceptable", "pass_fail": "True", "issues_summary": ["Severe glare detected"], "recommendations": ["📱 This is a mobile_app_screenshot - physical document checks have been adjusted.", "⚠️ Resolution too low for reliable OCR. Rescan at higher resolution.", "⚠️ Significant glare detected. Adjust lighting and recapture.", "🔄 Image quality insufficient. Please capture again with better camera/scanner settings.", "✅ Image sharpness is excellent."]}, "detailed_results": {"resolution": {"dimensions": {"width": 2448, "height": 3264, "megapixels": 7.99}, "dpi": {"horizontal": 96, "vertical": 96, "average": 96.0}, "quality": {"score": 100.0, "level": "Digital Quality", "meets_ocr_requirements": false}, "aspect_ratio": {"actual": 0.75, "expected": 0.37, "deviation_percent": 104.0}, "recommendations": ["⚠️ Resolution too low for reliable OCR. Rescan at higher resolution.", "🔄 Image quality insufficient. Please capture again with better camera/scanner settings."]}, "blur": {"metrics": {"laplacian_variance": 259.74, "is_blurry": false, "blur_score": 46.61, "blur_level": "<PERSON>"}, "motion_blur": {"detected": "False", "score": 5.399242854310842, "direction": "vertical"}, "focus_distribution": {"sharp_areas_percent": 36.5, "uniform_sharpness": false}, "recommendations": ["✅ Image sharpness is excellent."]}, "glare": {"exposure_metrics": {"mean_brightness": 198.1, "overexposed_percent": "27.3", "is_overexposed": "True", "contrast_ratio": 0.28}, "glare_analysis": {"glare_score": 34.4, "glare_level": "Severe", "num_glare_spots": 115, "glare_coverage_percent": 0.35, "glare_patterns": {"type": "multiple", "description": "Multiple glare spots detected"}}, "affected_regions": [{"bbox": ["302", "181", "15", "987"], "center": [304, 690], "area": "1983", "intensity": 226.6350557244174}, {"bbox": ["1295", "730", "5", "59"], "center": [1296, 759], "area": "151", "intensity": 248.92203389830507}, {"bbox": ["1000", "753", "7", "35"], "center": [1002, 772], "area": "106", "intensity": 224.15102040816328}, {"bbox": ["982", "759", "12", "31"], "center": [990, 775], "area": "103", "intensity": 173.002688172043}, {"bbox": ["2165", "761", "3", "86"], "center": [2166, 802], "area": "162", "intensity": 250.85658914728683}], "recommendations": ["📷 Image is overexposed. Reduce camera exposure or lighting.", "⚠️ Significant glare detected. Adjust lighting and recapture."]}, "completeness": {"boundary_detected": true, "completeness_score": 100.0, "completeness_level": "Digital Document", "edge_analysis": {"edge_coverage": 100.0, "has_gaps": false, "num_gaps": 0}, "corner_analysis": {"visible_corners": 4, "missing_corners": [], "is_rectangular": true}, "issues": [], "boundary_points": [], "recommendations": ["✅ Digital screenshot - no physical boundaries to check."]}, "damage": {"damage_score": 100.0, "damage_level": "Digital (No Physical Damage)", "damage_types": [], "stain_analysis": {"count": 0, "coverage_percent": 0.0, "regions": []}, "tear_analysis": {"count": 0, "max_length": 0, "regions": []}, "fold_analysis": {"count": 0, "pattern": "none", "lines": []}, "recommendations": ["✅ Digital image - no physical damage possible."]}}, "score_breakdown": {"resolution": {"score": 100.0, "weight": 0.2, "contribution": 20.0}, "blur": {"score": 46.61, "weight": 0.25, "contribution": 11.6525}, "glare": {"score": 34.4, "weight": 0.2, "contribution": 6.88}, "completeness": {"score": 100.0, "weight": 0.2, "contribution": 20.0}, "damage": {"score": 100.0, "weight": 0.15, "contribution": 15.0}}, "assessment_method": "opencv", "quality_passed": "True", "quality_score": 73.5, "quality_level": "Acceptable", "main_issues": ["Severe glare detected"], "top_recommendations": ["📱 This is a mobile_app_screenshot - physical document checks have been adjusted.", "⚠️ Resolution too low for reliable OCR. Rescan at higher resolution.", "⚠️ Significant glare detected. Adjust lighting and recapture."]}