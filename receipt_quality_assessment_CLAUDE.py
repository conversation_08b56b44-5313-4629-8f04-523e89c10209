import os
import json
import base64
from pathlib import Path
from typing import List, Dict, Any
from datetime import datetime
import asyncio
import aiohttp
from pydantic import BaseModel, Field
from PIL import Image


class QualityIssue(BaseModel):
    """Individual quality issue assessment"""
    detected: bool = Field(description="Whether the issue was detected (True/False)")
    severity_level: str = Field(description="Severity level: 'low', 'medium', 'high', 'critical'")
    confidence_score: float = Field(description="Confidence in detection (0.0-1.0)", ge=0.0, le=1.0)
    quantitative_measure: float = Field(description="Quantitative measure when applicable (e.g., blur intensity, damage percentage)")
    description: str = Field(description="Short description of the finding in one sentence")
    recommendation: str = Field(description="Simple recommendation to address the issue")


class ImageQualityAssessment(BaseModel):
    """Complete image quality assessment results"""
    blur_detection: QualityIssue = Field(description="Blur detection assessment")
    contrast_assessment: QualityIssue = Field(description="Contrast quality assessment")
    glare_identification: QualityIssue = Field(description="Glare detection assessment")
    water_stains: QualityIssue = Field(description="Water stain damage detection")
    tears_or_folds: QualityIssue = Field(description="Physical tears or fold detection")
    cut_off_detection: QualityIssue = Field(description="Edge cut-off detection")
    missing_sections: QualityIssue = Field(description="Missing content sections detection")
    obstructions: QualityIssue = Field(description="Obstruction detection")

    overall_quality_score: int = Field(description="Overall quality score from 1-10", ge=1, le=10)
    overall_confidence: float = Field(description="Overall confidence in assessment (0.0-1.0)", ge=0.0, le=1.0)
    suitable_for_extraction: bool = Field(description="Whether image is suitable for OCR/data extraction")
    primary_issues: List[str] = Field(description="List of primary quality issues affecting extraction")
    assessment_reasoning: str = Field(description="Brief explanation of the overall assessment reasoning")


class ReceiptQualityAssessor:
    """Main class for receipt image quality assessment using Claude"""
    
    def __init__(self, api_key: str, model: str = "claude-3-7-sonnet-20250109"):
        self.api_key = api_key
        self.model = model
        self.base_url = "https://api.anthropic.com/v1/messages"
        
    def encode_image(self, image_path: str) -> tuple[str, str]:
        """Encode image to base64 string and return with correct media type"""
        try:
            # First, get the correct media type
            media_type = self.get_media_type_from_image(image_path)
            
            # If the image needs conversion (unsupported format), convert to JPEG
            with Image.open(image_path) as img:
                if img.format and img.format.lower() not in ['jpeg', 'jpg', 'png', 'webp', 'gif']:
                    # Convert to RGB if necessary and save as JPEG
                    if img.mode in ('RGBA', 'LA', 'P'):
                        # Create white background for transparent images
                        background = Image.new('RGB', img.size, (255, 255, 255))
                        if img.mode == 'P':
                            img = img.convert('RGBA')
                        background.paste(img, mask=img.split()[-1] if img.mode in ('RGBA', 'LA') else None)
                        img = background
                    elif img.mode != 'RGB':
                        img = img.convert('RGB')
                    
                    # Save to bytes
                    import io
                    img_byte_arr = io.BytesIO()
                    img.save(img_byte_arr, format='JPEG', quality=95)
                    img_byte_arr = img_byte_arr.getvalue()
                    return base64.b64encode(img_byte_arr).decode('utf-8'), "image/jpeg"
                else:
                    # Use original file
                    with open(image_path, "rb") as image_file:
                        return base64.b64encode(image_file.read()).decode('utf-8'), media_type
                        
        except Exception as e:
            raise Exception(f"Error encoding image {image_path}: {str(e)}")
    
    def get_image_info(self, image_path: str) -> Dict[str, Any]:
        """Get basic image information"""
        try:
            with Image.open(image_path) as img:
                return {
                    "width": img.width,
                    "height": img.height,
                    "format": img.format,
                    "mode": img.mode,
                    "size_mb": os.path.getsize(image_path) / (1024 * 1024)
                }
        except Exception as e:
            return {"error": str(e)}
    
    def get_media_type_from_image(self, image_path: str) -> str:
        """Determine the correct media type by reading the actual image format"""
        try:
            with Image.open(image_path) as img:
                format_lower = img.format.lower() if img.format else None
                if format_lower in ['jpeg', 'jpg']:
                    return "image/jpeg"
                elif format_lower == 'png':
                    return "image/png"
                elif format_lower == 'webp':
                    return "image/webp"
                elif format_lower in ['gif']:
                    return "image/gif"
                else:
                    # Default to jpeg for unsupported formats, but convert first
                    return "image/jpeg"
        except Exception:
            # Fallback to jpeg if we can't determine the format
            return "image/jpeg"
    
    def create_assessment_prompt(self) -> str:
        """Create the professional prompt for image quality assessment"""
        return """You are an expert image quality analyst specializing in receipt and invoice document assessment. Your task is to thoroughly analyze the provided receipt/invoice image and assess its quality across multiple dimensions before OCR/data extraction processing.

ANALYSIS REQUIREMENTS:

1. **BLUR DETECTION**: Examine text sharpness, edge definition, and overall focus quality. Look for motion blur, camera shake, or out-of-focus areas that would impair text recognition.
   - Provide quantitative_measure: blur intensity (0.0=sharp, 1.0=extremely blurry)
   - Assess severity_level and confidence_score

2. **CONTRAST ASSESSMENT**: Evaluate the contrast between text and background. Check for adequate differentiation that enables clear text recognition.
   - Provide quantitative_measure: contrast ratio assessment (0.0=poor, 1.0=excellent)
   - Consider lighting conditions and background uniformity

3. **GLARE IDENTIFICATION**: Detect bright spots, reflections, or glare that obscure text or important document areas. Look for overexposed regions.
   - Provide quantitative_measure: percentage of image affected by glare (0.0-1.0)
   - Identify specific areas where glare impacts readability

4. **WATER STAIN DETECTION**: Identify water damage including discoloration, staining, warping effects, or color distortions that affect document readability.
   - Provide quantitative_measure: percentage of document affected (0.0-1.0)
   - Assess impact on text legibility

5. **TEARS OR FOLDS DETECTION**: Look for physical damage like tears, creases, folds, or wrinkles that may cause text distortion or information loss.
   - Provide quantitative_measure: severity of physical damage (0.0=none, 1.0=severe)
   - Count visible fold lines or tear areas

6. **CUT-OFF DETECTION**: Check if document edges are cut off or if the image frame excludes important document portions.
   - Provide quantitative_measure: percentage of document potentially cut off (0.0-1.0)
   - Identify which edges are affected

7. **MISSING SECTIONS**: Identify if parts of the receipt/invoice are missing, incomplete, or not captured in the image.
   - Provide quantitative_measure: estimated percentage of content missing (0.0-1.0)
   - Consider typical receipt structure

8. **OBSTRUCTIONS**: Detect any objects, fingers, shadows, or other elements that block or obscure document content.
   - Provide quantitative_measure: percentage of document obscured (0.0-1.0)
   - Identify types of obstructions

ASSESSMENT CRITERIA:
- For each quality issue, determine if it's detected (True/False)
- Assign severity_level: 'low', 'medium', 'high', 'critical'
- Provide confidence_score (0.0-1.0) for your detection confidence
- Include quantitative_measure for measurable aspects
- Provide a concise, factual description in one sentence
- Give practical recommendations
- Assign an overall quality score (1-10, where 10 is perfect quality)
- Provide overall_confidence (0.0-1.0) for the entire assessment
- Determine if the image is suitable for OCR/data extraction
- List primary_issues that most affect extraction quality
- Provide assessment_reasoning explaining your overall judgment

IMPORTANT GUIDELINES:
- Focus specifically on receipt/invoice characteristics (structured text, tables, line items, totals)
- Be thorough but practical in your assessment
- Consider the impact on automated text extraction systems
- Prioritize issues that would significantly impair data extraction accuracy
- Use quantitative measures to provide objective assessments where possible

Analyze the provided image and return your assessment in the following JSON structure:

{
  "blur_detection": {
    "detected": boolean,
    "severity_level": "low|medium|high|critical",
    "confidence_score": float (0.0-1.0),
    "quantitative_measure": float (blur intensity 0.0-1.0),
    "description": "string",
    "recommendation": "string"
  },
  "contrast_assessment": {
    "detected": boolean,
    "severity_level": "low|medium|high|critical",
    "confidence_score": float (0.0-1.0),
    "quantitative_measure": float (contrast quality 0.0-1.0),
    "description": "string",
    "recommendation": "string"
  },
  "glare_identification": {
    "detected": boolean,
    "severity_level": "low|medium|high|critical",
    "confidence_score": float (0.0-1.0),
    "quantitative_measure": float (percentage affected 0.0-1.0),
    "description": "string",
    "recommendation": "string"
  },
  "water_stains": {
    "detected": boolean,
    "severity_level": "low|medium|high|critical",
    "confidence_score": float (0.0-1.0),
    "quantitative_measure": float (percentage affected 0.0-1.0),
    "description": "string",
    "recommendation": "string"
  },
  "tears_or_folds": {
    "detected": boolean,
    "severity_level": "low|medium|high|critical",
    "confidence_score": float (0.0-1.0),
    "quantitative_measure": float (damage severity 0.0-1.0),
    "description": "string",
    "recommendation": "string"
  },
  "cut_off_detection": {
    "detected": boolean,
    "severity_level": "low|medium|high|critical",
    "confidence_score": float (0.0-1.0),
    "quantitative_measure": float (percentage cut off 0.0-1.0),
    "description": "string",
    "recommendation": "string"
  },
  "missing_sections": {
    "detected": boolean,
    "severity_level": "low|medium|high|critical",
    "confidence_score": float (0.0-1.0),
    "quantitative_measure": float (percentage missing 0.0-1.0),
    "description": "string",
    "recommendation": "string"
  },
  "obstructions": {
    "detected": boolean,
    "severity_level": "low|medium|high|critical",
    "confidence_score": float (0.0-1.0),
    "quantitative_measure": float (percentage obscured 0.0-1.0),
    "description": "string",
    "recommendation": "string"
  },
  "overall_quality_score": integer (1-10),
  "overall_confidence": float (0.0-1.0),
  "suitable_for_extraction": boolean,
  "primary_issues": ["list", "of", "main", "issues"],
  "assessment_reasoning": "string explaining overall judgment"
}

Return only the JSON response with no additional text."""

    async def assess_image_quality(self, image_path: str) -> ImageQualityAssessment:
        """Assess image quality using Claude vision capabilities"""
        
        # Encode image and get correct media type
        base64_image, media_type = self.encode_image(image_path)
        
        # Get image info
        img_info = self.get_image_info(image_path)
        
        # Prepare the request
        headers = {
            "Content-Type": "application/json",
            "x-api-key": self.api_key,
            "anthropic-version": "2023-06-01"
        }
        
        payload = {
            "model": self.model,
            "max_tokens": 1500,
            "messages": [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": f"Please analyze this receipt/invoice image for quality assessment. Image info: {img_info}\n\n{self.create_assessment_prompt()}"
                        },
                        {
                            "type": "image",
                            "source": {
                                "type": "base64",
                                "media_type": media_type,
                                "data": base64_image
                            }
                        }
                    ]
                }
            ]
        }
        
        # Make API request
        async with aiohttp.ClientSession() as session:
            try:
                async with session.post(self.base_url, headers=headers, json=payload) as response:
                    if response.status != 200:
                        error_text = await response.text()
                        raise Exception(f"API request failed with status {response.status}: {error_text}")
                    
                    result = await response.json()
                    
                    # Parse the response
                    content = result['content'][0]['text']
                    
                    # Extract JSON from the response
                    try:
                        # Try to find JSON in the response
                        start_idx = content.find('{')
                        end_idx = content.rfind('}') + 1
                        if start_idx != -1 and end_idx != 0:
                            json_str = content[start_idx:end_idx]
                            assessment_data = json.loads(json_str)
                        else:
                            assessment_data = json.loads(content)
                    except json.JSONDecodeError:
                        raise Exception(f"Failed to parse JSON response: {content}")
                    
                    return ImageQualityAssessment(**assessment_data)
                    
            except Exception as e:
                raise Exception(f"Error during API request for {image_path}: {str(e)}")

    async def process_image_folder(self, folder_path: str, output_file: str = None) -> Dict[str, Any]:
        """Process all images in a folder and return assessment results"""
        
        folder = Path(folder_path)
        if not folder.exists():
            raise FileNotFoundError(f"Folder not found: {folder_path}")
        
        # Get all image files
        image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'}
        image_files = [f for f in folder.iterdir() 
                      if f.is_file() and f.suffix.lower() in image_extensions]
        
        if not image_files:
            raise ValueError(f"No image files found in {folder_path}")
        
        print(f"Found {len(image_files)} images to process...")
        
        results = {
            "assessment_timestamp": datetime.now().isoformat(),
            "total_images": len(image_files),
            "assessments": {},
            "summary": {
                "suitable_for_extraction": 0,
                "average_quality_score": 0,
                "common_issues": {}
            }
        }
        
        # Process images
        for i, image_file in enumerate(image_files, 1):
            print(f"Processing {i}/{len(image_files)}: {image_file.name}")
            
            try:
                assessment = await self.assess_image_quality(str(image_file))
                results["assessments"][image_file.name] = assessment.model_dump()
                
                # Update summary statistics
                if assessment.suitable_for_extraction:
                    results["summary"]["suitable_for_extraction"] += 1
                
                # Track common issues
                for field_name, issue in assessment.model_dump().items():
                    if isinstance(issue, dict) and issue.get("detected", False):
                        if field_name not in results["summary"]["common_issues"]:
                            results["summary"]["common_issues"][field_name] = 0
                        results["summary"]["common_issues"][field_name] += 1
                
                print(f"✓ Completed: {image_file.name} (Quality Score: {assessment.overall_quality_score}/10)")
                
            except Exception as e:
                print(f"✗ Error processing {image_file.name}: {str(e)}")
                results["assessments"][image_file.name] = {"error": str(e)}
        
        # Calculate summary statistics
        valid_assessments = [a for a in results["assessments"].values() if "error" not in a]
        if valid_assessments:
            total_score = sum(a["overall_quality_score"] for a in valid_assessments)
            results["summary"]["average_quality_score"] = round(total_score / len(valid_assessments), 2)
        
        # Save results
        if output_file:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(results, f, indent=2, ensure_ascii=False)
            print(f"\nResults saved to: {output_file}")
        
        return results

    def print_summary(self, results: Dict[str, Any]):
        """Print a summary of the assessment results"""
        summary = results["summary"]
        total = results["total_images"]
        
        print("\n" + "="*60)
        print("RECEIPT IMAGE QUALITY ASSESSMENT SUMMARY")
        print("="*60)
        print(f"Total Images Processed: {total}")
        print(f"Suitable for Extraction: {summary['suitable_for_extraction']}/{total} ({summary['suitable_for_extraction']/total*100:.1f}%)")
        print(f"Average Quality Score: {summary['average_quality_score']}/10")
        
        if summary["common_issues"]:
            print(f"\nMost Common Issues:")
            sorted_issues = sorted(summary["common_issues"].items(), key=lambda x: x[1], reverse=True)
            for issue, count in sorted_issues[:5]:
                print(f"  • {issue.replace('_', ' ').title()}: {count} images ({count/total*100:.1f}%)")
        
        print("\n" + "="*60)


async def main():
    """Main function to run the receipt quality assessment"""
    
    # Configuration - Update these values as needed
    ANTHROPIC_API_KEY = "************************************************************************************************************"
    IMAGES_FOLDER = "blur_dataset"             
    OUTPUT_FILE = "quality_assessment_results_CLAUDE-OPUS-4.json"  
    MODEL = "claude-opus-4-20250514"                        
    
    # Initialize assessor
    assessor = ReceiptQualityAssessor(ANTHROPIC_API_KEY, MODEL)
    
    try:
        # Process images
        results = await assessor.process_image_folder(IMAGES_FOLDER, OUTPUT_FILE)
        
        # Print summary
        assessor.print_summary(results)
        
    except Exception as e:
        print(f"Error: {str(e)}")
        return 1
    
    return 0


if __name__ == "__main__":
    import sys
    sys.exit(asyncio.run(main()))