#!/usr/bin/env python3
"""
Test script for enhanced LLM quality assessment format
This verifies the output includes all quantitative parameters
"""

import os
import json
from pathlib import Path
from dotenv import load_dotenv
from agno.utils.log import logger

from llm_image_quality_assessor import LLMImageQualityAssessor

# Load environment variables
load_dotenv()

def test_enhanced_format():
    """Test that LLM assessment returns the enhanced format with quantitative parameters"""
    print("\n" + "="*70)
    print("TESTING ENHANCED LLM QUALITY ASSESSMENT FORMAT")
    print("="*70)
    
    # Test image path - update this to point to an actual image
    test_image = "expense_files/receipt_sample.jpg"  # Update this path
    
    if not os.path.exists(test_image):
        print(f"❌ Test image not found: {test_image}")
        print("Please update the test_image path to point to an actual image file")
        return False
    
    try:
        print(f"\n🤖 Testing enhanced LLM assessment on: {test_image}")
        
        # Initialize LLM assessor
        assessor = LLMImageQualityAssessor()
        
        # Perform assessment
        assessment = assessor.assess_image_quality_sync(test_image)
        
        # Format for workflow
        result = assessor.format_assessment_for_workflow(assessment, test_image)
        
        print(f"✅ LLM Assessment completed successfully")
        
        # Verify the format includes all enhanced fields
        quality_issues = [
            'blur_detection', 'contrast_assessment', 'glare_identification',
            'water_stains', 'tears_or_folds', 'cut_off_detection',
            'missing_sections', 'obstructions'
        ]
        
        required_issue_fields = ['detected', 'severity_level', 'confidence_score', 
                               'quantitative_measure', 'description', 'recommendation']
        
        print(f"\n🔍 Verifying enhanced format:")
        format_correct = True
        
        # Check each quality issue
        for issue_name in quality_issues:
            if issue_name in result:
                issue = result[issue_name]
                print(f"\n   📋 {issue_name}:")
                
                for field in required_issue_fields:
                    if field in issue:
                        value = issue[field]
                        if field == 'detected':
                            print(f"      ✅ {field}: {value} (boolean)")
                        elif field == 'severity_level':
                            print(f"      ✅ {field}: '{value}' (should be low/medium/high/critical)")
                        elif field in ['confidence_score', 'quantitative_measure']:
                            print(f"      ✅ {field}: {value} (float 0.0-1.0)")
                        else:
                            print(f"      ✅ {field}: '{value[:50]}...' (string)")
                    else:
                        print(f"      ❌ {field}: missing")
                        format_correct = False
            else:
                print(f"   ❌ {issue_name}: missing")
                format_correct = False
        
        # Check overall fields
        print(f"\n   📊 Overall Assessment:")
        if 'overall_quality_score' in result:
            score = result['overall_quality_score']
            print(f"      ✅ overall_quality_score: {score} (1-10)")
        else:
            print(f"      ❌ overall_quality_score: missing")
            format_correct = False
            
        if 'suitable_for_extraction' in result:
            suitable = result['suitable_for_extraction']
            print(f"      ✅ suitable_for_extraction: {suitable} (boolean)")
        else:
            print(f"      ❌ suitable_for_extraction: missing")
            format_correct = False
        
        # Show sample enhanced issue
        print(f"\n📄 Sample enhanced quality issue format:")
        if 'blur_detection' in result:
            blur_issue = result['blur_detection']
            print(f"   blur_detection: {{")
            for key, value in blur_issue.items():
                if isinstance(value, str):
                    print(f"     '{key}': '{value}',")
                else:
                    print(f"     '{key}': {value},")
            print(f"   }}")
        
        # Save test result
        output_file = f"enhanced_llm_test_result_{Path(test_image).stem}.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, indent=2, ensure_ascii=False)
        print(f"\n💾 Test result saved to: {output_file}")
        
        if format_correct:
            print(f"\n✅ ENHANCED FORMAT VERIFICATION PASSED")
            print(f"   - All quality issues present with enhanced fields")
            print(f"   - Quantitative parameters included")
            print(f"   - Severity levels and confidence scores present")
            print(f"   - Structure matches enhanced format specification")
        else:
            print(f"\n❌ ENHANCED FORMAT VERIFICATION FAILED")
            print(f"   - Check the issues listed above")
        
        return format_correct
        
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        return False

def show_enhanced_format_example():
    """Show what the enhanced format looks like"""
    print("\n" + "="*70)
    print("ENHANCED FORMAT SPECIFICATION")
    print("="*70)
    
    enhanced_example = {
        "blur_detection": {
            "detected": True,
            "severity_level": "medium",
            "confidence_score": 0.85,
            "quantitative_measure": 0.3,
            "description": "Moderate blur detected in lower portion of receipt",
            "recommendation": "Retake image with steadier camera position"
        },
        "contrast_assessment": {
            "detected": False,
            "severity_level": "low",
            "confidence_score": 0.9,
            "quantitative_measure": 0.8,
            "description": "Good contrast between text and background",
            "recommendation": "No action needed for contrast"
        },
        "overall_quality_score": 7,
        "suitable_for_extraction": True
    }
    
    print(f"📋 Enhanced Format Example:")
    print(json.dumps(enhanced_example, indent=2))
    
    print(f"\n🎯 Enhanced Fields Explained:")
    print(f"   • detected: Boolean - whether the issue was found")
    print(f"   • severity_level: String - 'low', 'medium', 'high', 'critical'")
    print(f"   • confidence_score: Float (0.0-1.0) - LLM's confidence in detection")
    print(f"   • quantitative_measure: Float (0.0-1.0) - measurable intensity/percentage")
    print(f"   • description: String - one sentence description")
    print(f"   • recommendation: String - practical suggestion")
    
    return True

def main():
    """Main test function"""
    print("🚀 Starting Enhanced LLM Quality Assessment Format Tests")
    
    # Check API key
    if not os.getenv("ANTHROPIC_API_KEY"):
        print("❌ ANTHROPIC_API_KEY environment variable not set")
        print("Please set your Anthropic API key in the .env file")
        return
    
    # Run tests
    try:
        # Test 1: Show format specification
        test1_success = show_enhanced_format_example()
        
        # Test 2: Actual enhanced format test
        test2_success = test_enhanced_format()
        
        print("\n" + "="*70)
        if test1_success and test2_success:
            print("✅ ALL TESTS PASSED")
            print("✅ LLM assessment now uses enhanced format")
            print("✅ All quantitative parameters included")
            print("✅ Severity levels and confidence scores present")
            print("✅ Ready for production use")
        else:
            print("❌ SOME TESTS FAILED")
        print("="*70)
        
    except Exception as e:
        print(f"\n❌ Test execution failed: {str(e)}")

if __name__ == "__main__":
    main()
