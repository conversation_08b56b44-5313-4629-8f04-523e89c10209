#!/usr/bin/env python3
"""
Test script for Enhanced LLM Quality Assessment UQLM Validation with Judge Assessments
"""

import os
import json
import asyncio
from pathlib import Path
from agno.utils.log import logger
from llm_image_quality_assessor import LLMImageQualityAssessor
from llm_quality_validator import ImageQualityUQLMValidator
from langchain_anthropic import ChatAnthropic

async def test_enhanced_llm_validation():
    """Test the enhanced LLM quality assessment with judge assessments and UQLM validation"""
    
    # Test image path
    test_image = "expense_files/german_file_4.png"
    
    if not Path(test_image).exists():
        logger.error(f"Test image not found: {test_image}")
        return
    
    logger.info("🚀 Starting Enhanced LLM Quality Assessment UQLM Validation Test")
    logger.info("📋 This test includes:")
    logger.info("   1. Original LLM Quality Assessment")
    logger.info("   2. Independent Judge LLM Assessments")
    logger.info("   3. UQLM Validation of Original Assessment")
    logger.info("   4. Judge Consensus Analysis")
    
    try:
        # Step 1: Perform Original LLM Quality Assessment
        logger.info("🤖 Step 1: Performing original LLM quality assessment...")
        llm_assessor = LLMImageQualityAssessor()
        assessment = llm_assessor.assess_image_quality_sync(test_image)
        llm_result = llm_assessor.format_assessment_for_workflow(assessment, test_image)
        
        logger.info(f"✅ Original LLM Assessment completed - Score: {llm_result['overall_quality_score']}/10")
        
        # Step 2: Perform Enhanced UQLM Validation with Judge Assessments
        logger.info("🎯 Step 2: Performing enhanced UQLM validation with judge assessments...")
        
        # Initialize validator
        primary_llm = ChatAnthropic(model="claude-3-7-sonnet-20250219", 
                                  api_key=os.getenv("ANTHROPIC_API_KEY"))
        validator = ImageQualityUQLMValidator(primary_llm, logger)
        
        # Run enhanced validation
        validation_result = await validator.validate_quality_assessment(
            llm_assessment=llm_result,
            image_path=test_image,
            opencv_assessment=None  # No OpenCV comparison for this test
        )
        
        logger.info(f"✅ Enhanced UQLM Validation completed")
        logger.info(f"    🎯 Validation Confidence: {validation_result['validation_summary']['overall_confidence']:.2f}")
        logger.info(f"    📊 Reliability Level: {validation_result['validation_summary']['reliability_level']}")
        logger.info(f"    ✅ Is Reliable: {validation_result['validation_summary']['is_reliable']}")
        
        # Step 3: Analyze Judge Assessments
        judge_assessments = validation_result.get('judge_assessments', {})
        if judge_assessments:
            logger.info("🤖 Step 3: Analyzing judge assessments...")
            for judge_name, judge_assessment in judge_assessments.items():
                if "error" not in judge_assessment:
                    score = judge_assessment.get('overall_quality_score', 0)
                    suitable = judge_assessment.get('suitable_for_extraction', False)
                    logger.info(f"    • {judge_name}: {score}/10, Suitable: {suitable}")
                else:
                    logger.warning(f"    • {judge_name}: Assessment failed - {judge_assessment.get('error', 'Unknown error')}")
        
        # Step 4: Analyze Judge Consensus
        judge_consensus = validation_result.get('judge_consensus', {})
        if judge_consensus and "error" not in judge_consensus:
            logger.info("📊 Step 4: Judge consensus analysis...")
            score_consensus = judge_consensus.get('score_consensus', {})
            avg_score = score_consensus.get('average_score', 0)
            score_agreement = score_consensus.get('score_agreement', 'unknown')
            overall_agreement = judge_consensus.get('overall_agreement', 'unknown')
            
            logger.info(f"    📊 Average Judge Score: {avg_score:.1f}/10")
            logger.info(f"    🎯 Score Agreement: {score_agreement}")
            logger.info(f"    ✅ Overall Agreement: {overall_agreement}")
            
            suitability = judge_consensus.get('suitability_consensus', {})
            suitable_pct = suitability.get('suitable_percentage', 0)
            unanimous = suitability.get('unanimous', False)
            logger.info(f"    📋 Suitability Consensus: {suitable_pct:.0f}% agree, Unanimous: {unanimous}")
        
        # Step 5: Save Enhanced Results
        logger.info("💾 Step 5: Saving enhanced test results...")
        
        # Save original LLM assessment
        with open("test_original_llm_assessment.json", 'w', encoding='utf-8') as f:
            json.dump(llm_result, f, indent=2, ensure_ascii=False)
        
        # Save enhanced validation results (includes judge assessments and consensus)
        with open("test_enhanced_validation_results.json", 'w', encoding='utf-8') as f:
            json.dump(validation_result, f, indent=2, ensure_ascii=False, default=str)
        
        logger.info("✅ Enhanced test results saved:")
        logger.info("   📄 test_original_llm_assessment.json")
        logger.info("   📄 test_enhanced_validation_results.json")
        
        # Step 6: Display Comprehensive Summary
        logger.info("📋 Comprehensive Test Summary:")
        logger.info(f"    🤖 Original LLM Score: {llm_result['overall_quality_score']}/10")
        
        if judge_assessments:
            valid_judges = {k: v for k, v in judge_assessments.items() if "error" not in v}
            if valid_judges:
                judge_scores = [v.get('overall_quality_score', 0) for v in valid_judges.values()]
                avg_judge_score = sum(judge_scores) / len(judge_scores)
                logger.info(f"    👥 Average Judge Score: {avg_judge_score:.1f}/10")
                logger.info(f"    📊 Score Range: {min(judge_scores)}-{max(judge_scores)}/10")
        
        logger.info(f"    🎯 Validation Confidence: {validation_result['validation_summary']['overall_confidence']:.2f}")
        logger.info(f"    📊 Reliability: {validation_result['validation_summary']['reliability_level']}")
        
        # Show dimensional analysis
        dimensional = validation_result.get('dimensional_analysis', {})
        logger.info("📊 Validation Dimensional Analysis:")
        for dim_name, dim_result in dimensional.items():
            if hasattr(dim_result, 'confidence_score'):
                confidence = dim_result.confidence_score
                reliability = dim_result.reliability_level
                issues_count = len(dim_result.issues)
            else:
                confidence = dim_result.get('confidence_score', 0)
                reliability = dim_result.get('reliability_level', 'unknown')
                issues_count = len(dim_result.get('issues', []))
            
            logger.info(f"    • {dim_name}: {confidence:.2f} confidence, {reliability} reliability, {issues_count} issues")
        
        logger.info("🎉 Enhanced LLM Quality Assessment UQLM Validation test completed successfully!")
        logger.info("🔍 Key Benefits Demonstrated:")
        logger.info("   • Multiple LLM perspectives on image quality")
        logger.info("   • Consensus analysis for reliability assessment")
        logger.info("   • Comprehensive validation of original assessment")
        logger.info("   • Rich quantitative insights for decision making")
        
    except Exception as e:
        logger.error(f"❌ Enhanced test failed: {str(e)}")
        raise

if __name__ == "__main__":
    asyncio.run(test_enhanced_llm_validation())
