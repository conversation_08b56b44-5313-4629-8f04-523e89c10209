#!/usr/bin/env python3
"""
Test script for LLM-based image quality assessment integration
"""

import os
import json
import asyncio
from pathlib import Path
from dotenv import load_dotenv
from agno.utils.log import logger

from image_quality_processor import ImageQualityProcessor, assess_image_quality_llm, assess_image_quality_combined
from llm_image_quality_assessor import LLMImageQualityAssessor

# Load environment variables
load_dotenv()

def test_single_image_llm_assessment():
    """Test LLM assessment on a single image"""
    print("\n" + "="*60)
    print("TESTING LLM IMAGE QUALITY ASSESSMENT")
    print("="*60)
    
    # Test image path - update this to point to an actual image
    test_image = "expense_files/receipt_sample.jpg"  # Update this path
    
    if not os.path.exists(test_image):
        print(f"❌ Test image not found: {test_image}")
        print("Please update the test_image path to point to an actual image file")
        return
    
    try:
        # Test 1: Direct LLM assessment
        print(f"\n🤖 Testing direct LLM assessment on: {test_image}")
        result = assess_image_quality_llm(test_image)
        
        print(f"✅ LLM Assessment Results:")
        print(f"   Score: {result['quality_score']}/100")
        print(f"   Level: {result['quality_level']}")
        print(f"   Suitable for extraction: {result['quality_passed']}")
        print(f"   Primary issues: {result.get('llm_specific_results', {}).get('primary_issues', [])}")
        print(f"   Assessment reasoning: {result.get('llm_specific_results', {}).get('assessment_reasoning', 'N/A')}")
        
        # Save detailed results
        output_file = f"llm_quality_test_results_{Path(test_image).stem}.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, indent=2, ensure_ascii=False)
        print(f"📄 Detailed results saved to: {output_file}")
        
        return result
        
    except Exception as e:
        print(f"❌ LLM assessment failed: {str(e)}")
        return None

def test_combined_assessment():
    """Test combined OpenCV + LLM assessment"""
    print("\n" + "="*60)
    print("TESTING COMBINED OPENCV + LLM ASSESSMENT")
    print("="*60)
    
    # Test image path - update this to point to an actual image
    test_image = "expense_files/receipt_sample.jpg"  # Update this path
    
    if not os.path.exists(test_image):
        print(f"❌ Test image not found: {test_image}")
        return
    
    try:
        print(f"\n🔄 Testing combined assessment on: {test_image}")
        result = assess_image_quality_combined(test_image)
        
        print(f"✅ Combined Assessment Results:")
        print(f"   Primary Score (LLM): {result['quality_score']}/100")
        print(f"   Primary Level: {result['quality_level']}")
        print(f"   Suitable for extraction: {result['quality_passed']}")
        
        if 'comparison' in result:
            comparison = result['comparison']
            print(f"\n📊 Assessment Comparison:")
            print(f"   OpenCV Score: {comparison['opencv_score']}/100")
            print(f"   LLM Score: {comparison['llm_score']}/100")
            print(f"   Score Difference: {comparison['score_difference']}")
            print(f"   Pass/Fail Agreement: {comparison['pass_fail_agreement']}")
            print(f"   Assessment Consensus: {comparison['assessment_consensus']}")
        
        # Save detailed results
        output_file = f"combined_quality_test_results_{Path(test_image).stem}.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, indent=2, ensure_ascii=False)
        print(f"📄 Detailed results saved to: {output_file}")
        
        return result
        
    except Exception as e:
        print(f"❌ Combined assessment failed: {str(e)}")
        return None

def test_batch_assessment():
    """Test batch assessment on multiple images"""
    print("\n" + "="*60)
    print("TESTING BATCH LLM ASSESSMENT")
    print("="*60)
    
    # Test directory - update this to point to actual images
    test_dir = "expense_files"  # Update this path
    
    if not os.path.exists(test_dir):
        print(f"❌ Test directory not found: {test_dir}")
        return
    
    # Find image files
    image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'}
    image_files = [f for f in Path(test_dir).iterdir() 
                  if f.is_file() and f.suffix.lower() in image_extensions]
    
    if not image_files:
        print(f"❌ No image files found in: {test_dir}")
        return
    
    print(f"📁 Found {len(image_files)} images to assess")
    
    try:
        processor = ImageQualityProcessor(document_type='receipt', assessment_method='llm')
        results = []
        
        for i, image_file in enumerate(image_files[:3], 1):  # Test first 3 images
            print(f"\n🤖 Assessing {i}/{min(3, len(image_files))}: {image_file.name}")
            
            result = processor.assess_image_quality(str(image_file))
            results.append({
                'filename': image_file.name,
                'score': result.get('quality_score', 0),
                'level': result.get('quality_level', 'Unknown'),
                'suitable': result.get('quality_passed', False),
                'primary_issues': result.get('llm_specific_results', {}).get('primary_issues', [])
            })
            
            print(f"   Score: {result.get('quality_score', 0)}/100 ({result.get('quality_level', 'Unknown')})")
            print(f"   Suitable: {result.get('quality_passed', False)}")
        
        # Summary
        print(f"\n📊 Batch Assessment Summary:")
        suitable_count = sum(1 for r in results if r['suitable'])
        avg_score = sum(r['score'] for r in results) / len(results) if results else 0
        
        print(f"   Total assessed: {len(results)}")
        print(f"   Suitable for extraction: {suitable_count}/{len(results)} ({suitable_count/len(results)*100:.1f}%)")
        print(f"   Average score: {avg_score:.1f}/100")
        
        # Save batch results
        output_file = "batch_llm_quality_test_results.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        print(f"📄 Batch results saved to: {output_file}")
        
        return results
        
    except Exception as e:
        print(f"❌ Batch assessment failed: {str(e)}")
        return None

def main():
    """Main test function"""
    print("🚀 Starting LLM Image Quality Assessment Tests")
    
    # Check API key
    if not os.getenv("ANTHROPIC_API_KEY"):
        print("❌ ANTHROPIC_API_KEY environment variable not set")
        print("Please set your Anthropic API key in the .env file")
        return
    
    # Run tests
    try:
        # Test 1: Single image LLM assessment
        llm_result = test_single_image_llm_assessment()
        
        # Test 2: Combined assessment (if LLM worked)
        if llm_result:
            combined_result = test_combined_assessment()
        
        # Test 3: Batch assessment (if previous tests worked)
        if llm_result:
            batch_results = test_batch_assessment()
        
        print("\n" + "="*60)
        print("✅ ALL TESTS COMPLETED")
        print("="*60)
        print("Check the generated JSON files for detailed results")
        
    except Exception as e:
        print(f"\n❌ Test execution failed: {str(e)}")

if __name__ == "__main__":
    main()
