#!/usr/bin/env python3
"""
Test script for LLM Quality Assessment UQLM Validation
"""

import os
import json
import asyncio
from pathlib import Path
from agno.utils.log import logger
from llm_image_quality_assessor import LLMImageQualityAssessor
from llm_quality_validator import ImageQualityUQLMValidator
from langchain_anthropic import ChatAnthropic

async def test_llm_quality_validation():
    """Test the LLM quality assessment with UQLM validation"""
    
    # Test image path
    test_image = "expense_files/german_file_4.png"
    
    if not Path(test_image).exists():
        logger.error(f"Test image not found: {test_image}")
        return
    
    logger.info("🚀 Starting LLM Quality Assessment UQLM Validation Test")
    
    try:
        # Step 1: Perform LLM Quality Assessment
        logger.info("🤖 Step 1: Performing LLM quality assessment...")
        llm_assessor = LLMImageQualityAssessor()
        assessment = llm_assessor.assess_image_quality_sync(test_image)
        llm_result = llm_assessor.format_assessment_for_workflow(assessment, test_image)
        
        logger.info(f"✅ LLM Assessment completed - Score: {llm_result['overall_quality_score']}/10")
        
        # Step 2: Perform UQLM Validation
        logger.info("🎯 Step 2: Performing UQLM validation...")
        
        # Initialize validator
        primary_llm = ChatAnthropic(model="claude-3-7-sonnet-20250219", 
                                  api_key=os.getenv("ANTHROPIC_API_KEY"))
        validator = ImageQualityUQLMValidator(primary_llm, logger)
        
        # Run validation
        validation_result = await validator.validate_quality_assessment(
            llm_assessment=llm_result,
            image_path=test_image,
            opencv_assessment=None  # No OpenCV comparison for this test
        )
        
        logger.info(f"✅ UQLM Validation completed")
        logger.info(f"    🎯 Validation Confidence: {validation_result['validation_summary']['overall_confidence']:.2f}")
        logger.info(f"    📊 Reliability Level: {validation_result['validation_summary']['reliability_level']}")
        logger.info(f"    ✅ Is Reliable: {validation_result['validation_summary']['is_reliable']}")
        
        # Step 3: Save results
        logger.info("💾 Step 3: Saving test results...")
        
        # Save LLM assessment
        with open("test_llm_assessment.json", 'w', encoding='utf-8') as f:
            json.dump(llm_result, f, indent=2, ensure_ascii=False)
        
        # Save validation results
        with open("test_validation_results.json", 'w', encoding='utf-8') as f:
            json.dump(validation_result, f, indent=2, ensure_ascii=False, default=str)
        
        logger.info("✅ Test results saved to test_llm_assessment.json and test_validation_results.json")
        
        # Step 4: Display summary
        logger.info("📋 Test Summary:")
        logger.info(f"    🤖 LLM Score: {llm_result['overall_quality_score']}/10")
        logger.info(f"    🎯 Validation Confidence: {validation_result['validation_summary']['overall_confidence']:.2f}")
        logger.info(f"    📊 Reliability: {validation_result['validation_summary']['reliability_level']}")
        
        # Show dimensional analysis
        dimensional = validation_result.get('dimensional_analysis', {})
        logger.info("📊 Dimensional Analysis:")
        for dim_name, dim_result in dimensional.items():
            if hasattr(dim_result, 'confidence_score'):
                confidence = dim_result.confidence_score
                reliability = dim_result.reliability_level
                issues_count = len(dim_result.issues)
            else:
                confidence = dim_result.get('confidence_score', 0)
                reliability = dim_result.get('reliability_level', 'unknown')
                issues_count = len(dim_result.get('issues', []))
            
            logger.info(f"    • {dim_name}: {confidence:.2f} confidence, {reliability} reliability, {issues_count} issues")
        
        logger.info("🎉 LLM Quality Assessment UQLM Validation test completed successfully!")
        
    except Exception as e:
        logger.error(f"❌ Test failed: {str(e)}")
        raise

if __name__ == "__main__":
    asyncio.run(test_llm_quality_validation())
