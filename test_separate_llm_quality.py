#!/usr/bin/env python3
"""
Test script for separate LLM quality assessment integration
This tests the new parallel LLM assessment without affecting the existing workflow
"""

import os
import json
from pathlib import Path
from dotenv import load_dotenv
from agno.utils.log import logger

from llamaparse_extractor import assess_image_quality_llm_separate, assess_image_quality_before_extraction
from image_quality_processor import ImageQualityProcessor

# Load environment variables
load_dotenv()

def test_separate_assessments():
    """Test both OpenCV and LLM assessments running separately"""
    print("\n" + "="*70)
    print("TESTING SEPARATE OPENCV + LLM QUALITY ASSESSMENTS")
    print("="*70)
    
    # Test image path - update this to point to an actual image
    test_image = "expense_files/receipt_sample.jpg"  # Update this path
    
    if not os.path.exists(test_image):
        print(f"❌ Test image not found: {test_image}")
        print("Please update the test_image path to point to an actual image file")
        return
    
    test_file_path = Path(test_image)
    
    # Create output directories
    opencv_output_dir = Path("test_quality_reports")
    llm_output_dir = Path("test_llm_quality_reports")
    opencv_output_dir.mkdir(exist_ok=True)
    llm_output_dir.mkdir(exist_ok=True)
    
    try:
        print(f"\n📸 Testing separate assessments on: {test_image}")
        
        # Test 1: Original OpenCV assessment (unchanged)
        print(f"\n🔧 Running OpenCV Quality Assessment...")
        quality_processor = ImageQualityProcessor(document_type='receipt')
        opencv_result = assess_image_quality_before_extraction(test_file_path, quality_processor, opencv_output_dir)
        
        print(f"✅ OpenCV Assessment Results:")
        print(f"   Score: {opencv_result.get('quality_score', 'N/A')}/100")
        print(f"   Level: {opencv_result.get('quality_level', 'N/A')}")
        print(f"   Suitable: {opencv_result.get('quality_passed', 'N/A')}")
        
        # Test 2: New LLM assessment (separate)
        print(f"\n🤖 Running LLM Quality Assessment...")
        llm_result = assess_image_quality_llm_separate(test_file_path, llm_output_dir)
        
        print(f"✅ LLM Assessment Results:")
        print(f"   Score: {llm_result.get('quality_score', 'N/A')}/100")
        print(f"   Level: {llm_result.get('quality_level', 'N/A')}")
        print(f"   Suitable: {llm_result.get('quality_passed', 'N/A')}")
        
        if 'llm_specific_results' in llm_result:
            llm_specific = llm_result['llm_specific_results']
            print(f"   Primary Issues: {llm_specific.get('primary_issues', [])}")
            print(f"   Reasoning: {llm_specific.get('assessment_reasoning', 'N/A')[:100]}...")
        
        # Compare results
        print(f"\n📊 Comparison:")
        opencv_score = opencv_result.get('quality_score', 0)
        llm_score = llm_result.get('quality_score', 0)
        score_diff = abs(opencv_score - llm_score)
        
        print(f"   OpenCV Score: {opencv_score}/100")
        print(f"   LLM Score: {llm_score}/100")
        print(f"   Score Difference: {score_diff}")
        print(f"   Agreement: {'High' if score_diff < 20 else 'Low'}")
        
        # Check output files
        opencv_file = opencv_output_dir / f"{test_file_path.stem}_quality.json"
        llm_file = llm_output_dir / f"llm_quality_{test_file_path.stem}.json"
        
        print(f"\n📄 Output Files:")
        print(f"   OpenCV Report: {opencv_file} ({'✅ Exists' if opencv_file.exists() else '❌ Missing'})")
        print(f"   LLM Report: {llm_file} ({'✅ Exists' if llm_file.exists() else '❌ Missing'})")
        
        # Verify file contents are different
        if opencv_file.exists() and llm_file.exists():
            with open(opencv_file, 'r') as f:
                opencv_data = json.load(f)
            with open(llm_file, 'r') as f:
                llm_data = json.load(f)
            
            print(f"\n🔍 File Content Verification:")
            print(f"   OpenCV method: {opencv_data.get('assessment_method', 'opencv')}")
            print(f"   LLM method: {llm_data.get('assessment_method', 'llm')}")
            print(f"   Different structures: {'✅ Yes' if 'llm_specific_results' in llm_data else '❌ No'}")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        return False

def test_workflow_integration():
    """Test that the workflow can run both assessments"""
    print("\n" + "="*70)
    print("TESTING WORKFLOW INTEGRATION")
    print("="*70)
    
    # Test directory - update this to point to actual images
    test_dir = "expense_files"  # Update this path
    
    if not os.path.exists(test_dir):
        print(f"❌ Test directory not found: {test_dir}")
        return False
    
    # Find image files
    image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'}
    image_files = [f for f in Path(test_dir).iterdir() 
                  if f.is_file() and f.suffix.lower() in image_extensions]
    
    if not image_files:
        print(f"❌ No image files found in: {test_dir}")
        return False
    
    print(f"📁 Found {len(image_files)} images to test")
    
    # Create output directories
    opencv_output_dir = Path("test_quality_reports")
    llm_output_dir = Path("test_llm_quality_reports")
    opencv_output_dir.mkdir(exist_ok=True)
    llm_output_dir.mkdir(exist_ok=True)
    
    try:
        quality_processor = ImageQualityProcessor(document_type='receipt')
        
        # Test first 2 images
        for i, image_file in enumerate(image_files[:2], 1):
            print(f"\n📸 Testing {i}/2: {image_file.name}")
            
            # OpenCV assessment
            opencv_result = assess_image_quality_before_extraction(image_file, quality_processor, opencv_output_dir)
            print(f"   OpenCV: {opencv_result.get('quality_score', 0)}/100")
            
            # LLM assessment
            llm_result = assess_image_quality_llm_separate(image_file, llm_output_dir)
            print(f"   LLM: {llm_result.get('quality_score', 0)}/100")
        
        # Check output directories
        opencv_files = list(opencv_output_dir.glob("*_quality.json"))
        llm_files = list(llm_output_dir.glob("llm_quality_*.json"))
        
        print(f"\n📊 Results Summary:")
        print(f"   OpenCV reports generated: {len(opencv_files)}")
        print(f"   LLM reports generated: {len(llm_files)}")
        print(f"   Separate directories: {'✅ Yes' if opencv_output_dir != llm_output_dir else '❌ No'}")
        
        return True
        
    except Exception as e:
        print(f"❌ Workflow integration test failed: {str(e)}")
        return False

def main():
    """Main test function"""
    print("🚀 Starting Separate LLM Quality Assessment Tests")
    
    # Check API key
    if not os.getenv("ANTHROPIC_API_KEY"):
        print("❌ ANTHROPIC_API_KEY environment variable not set")
        print("Please set your Anthropic API key in the .env file")
        return
    
    # Run tests
    try:
        # Test 1: Separate assessments
        test1_success = test_separate_assessments()
        
        # Test 2: Workflow integration
        test2_success = test_workflow_integration()
        
        print("\n" + "="*70)
        if test1_success and test2_success:
            print("✅ ALL TESTS PASSED")
            print("✅ LLM quality assessment is working separately from OpenCV")
            print("✅ Both assessments save to different directories")
            print("✅ Existing workflow remains unchanged")
        else:
            print("❌ SOME TESTS FAILED")
        print("="*70)
        
    except Exception as e:
        print(f"\n❌ Test execution failed: {str(e)}")

if __name__ == "__main__":
    main()
