#!/usr/bin/env python3
"""
Test script for simplified LLM quality assessment format
This verifies the output matches the original simple format
"""

import os
import json
from pathlib import Path
from dotenv import load_dotenv
from agno.utils.log import logger

from llm_image_quality_assessor import LLMImageQualityAssessor

# Load environment variables
load_dotenv()

def test_simplified_format():
    """Test that LLM assessment returns the simplified format"""
    print("\n" + "="*60)
    print("TESTING SIMPLIFIED LLM QUALITY ASSESSMENT FORMAT")
    print("="*60)
    
    # Test image path - update this to point to an actual image
    test_image = "expense_files/receipt_sample.jpg"  # Update this path
    
    if not os.path.exists(test_image):
        print(f"❌ Test image not found: {test_image}")
        print("Please update the test_image path to point to an actual image file")
        return False
    
    try:
        print(f"\n🤖 Testing simplified LLM assessment on: {test_image}")
        
        # Initialize LLM assessor
        assessor = LLMImageQualityAssessor()
        
        # Perform assessment
        assessment = assessor.assess_image_quality_sync(test_image)
        
        # Format for workflow
        result = assessor.format_assessment_for_workflow(assessment, test_image)
        
        print(f"✅ LLM Assessment completed successfully")
        
        # Verify the format matches the original structure
        expected_fields = [
            'blur_detection', 'contrast_assessment', 'glare_identification',
            'water_stains', 'tears_or_folds', 'cut_off_detection',
            'missing_sections', 'obstructions', 'overall_quality_score',
            'suitable_for_extraction'
        ]
        
        print(f"\n🔍 Verifying simplified format:")
        format_correct = True
        
        for field in expected_fields:
            if field in result:
                if field in ['overall_quality_score', 'suitable_for_extraction']:
                    print(f"   ✅ {field}: {result[field]}")
                else:
                    # Check that quality issue has the right structure
                    issue = result[field]
                    if isinstance(issue, dict) and 'detected' in issue and 'description' in issue and 'recommendation' in issue:
                        print(f"   ✅ {field}: detected={issue['detected']}")
                    else:
                        print(f"   ❌ {field}: incorrect structure")
                        format_correct = False
            else:
                print(f"   ❌ {field}: missing")
                format_correct = False
        
        # Check for unwanted complex fields
        unwanted_fields = ['severity_level', 'confidence_score', 'quantitative_measure', 
                          'primary_issues', 'assessment_reasoning', 'overall_confidence']
        
        print(f"\n🚫 Checking for unwanted complex fields:")
        for field in unwanted_fields:
            if field in result:
                print(f"   ❌ {field}: should not be present in simplified format")
                format_correct = False
            else:
                print(f"   ✅ {field}: correctly absent")
        
        # Show sample output
        print(f"\n📄 Sample quality issue format:")
        blur_issue = result.get('blur_detection', {})
        print(f"   blur_detection: {{")
        print(f"     'detected': {blur_issue.get('detected', 'N/A')},")
        print(f"     'description': '{blur_issue.get('description', 'N/A')}',")
        print(f"     'recommendation': '{blur_issue.get('recommendation', 'N/A')}'")
        print(f"   }}")
        
        # Save test result
        output_file = f"simplified_llm_test_result_{Path(test_image).stem}.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, indent=2, ensure_ascii=False)
        print(f"\n💾 Test result saved to: {output_file}")
        
        if format_correct:
            print(f"\n✅ FORMAT VERIFICATION PASSED")
            print(f"   - All expected fields present")
            print(f"   - No unwanted complex fields")
            print(f"   - Structure matches original format")
        else:
            print(f"\n❌ FORMAT VERIFICATION FAILED")
            print(f"   - Check the issues listed above")
        
        return format_correct
        
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        return False

def test_original_vs_simplified():
    """Compare original complex format vs simplified format"""
    print("\n" + "="*60)
    print("COMPARING ORIGINAL VS SIMPLIFIED FORMAT")
    print("="*60)
    
    # Show what the original complex format looked like
    original_complex = {
        "blur_detection": {
            "detected": True,
            "severity_level": "medium",
            "confidence_score": 0.8,
            "quantitative_measure": 0.3,
            "description": "Moderate blur detected in lower portion",
            "recommendation": "Retake with steadier camera"
        },
        "overall_quality_score": 7,
        "overall_confidence": 0.85,
        "suitable_for_extraction": True,
        "primary_issues": ["blur", "slight glare"],
        "assessment_reasoning": "Image has acceptable quality with minor issues..."
    }
    
    # Show what the simplified format looks like
    simplified_format = {
        "blur_detection": {
            "detected": True,
            "description": "Moderate blur detected in lower portion",
            "recommendation": "Retake with steadier camera"
        },
        "overall_quality_score": 7,
        "suitable_for_extraction": True
    }
    
    print(f"📊 Original Complex Format (what we had):")
    print(json.dumps(original_complex, indent=2))
    
    print(f"\n📋 Simplified Format (what we want):")
    print(json.dumps(simplified_format, indent=2))
    
    print(f"\n🎯 Key Changes:")
    print(f"   ❌ Removed: severity_level, confidence_score, quantitative_measure")
    print(f"   ❌ Removed: overall_confidence, primary_issues, assessment_reasoning")
    print(f"   ✅ Kept: detected, description, recommendation")
    print(f"   ✅ Kept: overall_quality_score, suitable_for_extraction")
    
    return True

def main():
    """Main test function"""
    print("🚀 Starting Simplified LLM Quality Assessment Format Tests")
    
    # Check API key
    if not os.getenv("ANTHROPIC_API_KEY"):
        print("❌ ANTHROPIC_API_KEY environment variable not set")
        print("Please set your Anthropic API key in the .env file")
        return
    
    # Run tests
    try:
        # Test 1: Format comparison
        test1_success = test_original_vs_simplified()
        
        # Test 2: Actual simplified format test
        test2_success = test_simplified_format()
        
        print("\n" + "="*60)
        if test1_success and test2_success:
            print("✅ ALL TESTS PASSED")
            print("✅ LLM assessment now uses simplified format")
            print("✅ Output matches original structure")
            print("✅ No unnecessary complex fields")
        else:
            print("❌ SOME TESTS FAILED")
        print("="*60)
        
    except Exception as e:
        print(f"\n❌ Test execution failed: {str(e)}")

if __name__ == "__main__":
    main()
